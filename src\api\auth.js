import apiClient from './client.js'

export const authApi = {
  /**
   * 管理员登录
   * @param {string} password - 管理员密码
   * @returns {Promise<Object>} 登录结果
   */
  async login(password) {
    try {
      const response = await apiClient.post('/auth/login', { password })
      return response
    } catch (error) {
      throw new Error(error.message || '登录失败')
    }
  },

  /**
   * 验证token有效性
   * @param {string} token - JWT token
   * @returns {Promise<Object>} 验证结果
   */
  async verify(token) {
    try {
      const response = await apiClient.post('/auth/verify', {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      return response
    } catch (error) {
      throw new Error(error.message || 'Token验证失败')
    }
  },

  /**
   * 刷新token
   * @param {string} token - 当前JWT token
   * @returns {Promise<Object>} 新的token信息
   */
  async refresh(token) {
    try {
      const response = await apiClient.post('/auth/refresh', {}, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      })
      return response
    } catch (error) {
      throw new Error(error.message || 'Token刷新失败')
    }
  }
}
